import { Button } from "@/components/ui/button";
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuLabel,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import { useOrganization } from "@/hooks/useOrganization";
import { useUserRoles } from "@/hooks/useUserRoles";
import { Building, Building2, ChevronDown, Edit, Home, Search, Stethoscope } from "lucide-react";
import { useEffect, useRef, useState } from "react";
import { useNavigate } from "react-router-dom";
import { toast } from "sonner";

// Helper function to get organization icon based on type
const getOrganizationIcon = (type: string, _level: number = 0) => {
  switch (type.toLowerCase()) {
    case 'health_system':
    case 'health_network':
      return Building2;
    case 'hospital':
      return Building;
    case 'clinic':
    case 'clinic_group':
      return Stethoscope;
    case 'practice':
    case 'department':
      return Home;
    default:
      return Building2;
  }
};

// Component for rendering hierarchical organization items
interface OrganizationItemProps {
  org: any;
  isSelected: boolean;
  onSelect: (orgId: string) => void;
  isHighlighted: boolean;
  isLoading: boolean;
}

function OrganizationItem({ org, isSelected, onSelect, isHighlighted, isLoading }: OrganizationItemProps) {
  const Icon = getOrganizationIcon(org.type, org.hierarchy_level || 0);
  const indentLevel = (org.hierarchy_level || 0) * 16; // 16px per level

  return (
    <DropdownMenuItem
      disabled={isLoading || isSelected}
      className={`flex items-center justify-between data-[highlighted]:bg-muted/50 hover:!bg-muted/50 ${
        isHighlighted ? "bg-muted/50" : ""
      }`}
      onSelect={(e) => {
        e.preventDefault();
        onSelect(org.id);
      }}
    >
      <div className="flex items-center gap-2 w-full" style={{ paddingLeft: `${indentLevel}px` }}>
        <Icon className="h-4 w-4 text-muted-foreground flex-shrink-0" />
        <div className="flex flex-col min-w-0 flex-1">
          <span className={`truncate ${org.hierarchy_level === 0 ? 'font-semibold' : 'font-medium'}`}>
            {org.name}
          </span>
          {org.type !== 'system' && (
            <span className="text-xs text-muted-foreground capitalize">
              {org.type.replace('_', ' ')}
            </span>
          )}
        </div>
        {isSelected && (
          <div className="text-xs text-muted-foreground">Current</div>
        )}
      </div>
    </DropdownMenuItem>
  );
}

export function OrganizationSelector() {
  const {
    currentOrg,
    availableOrgs,
    isLoading,
    switchOrganization,
    hasMultipleOrgs,
    isSystemAdmin
  } = useOrganization();
  const { isAdmin } = useUserRoles();
  const navigate = useNavigate();
  const searchInputRef = useRef<HTMLInputElement>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const [filteredOrganizations, setFilteredOrganizations] = useState(availableOrgs);
  const [searchTerm, setSearchTerm] = useState("");
  const [highlightedIndex, setHighlightedIndex] = useState(-1);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);

  // Update filtered organizations when available orgs change
  useEffect(() => {
    setFilteredOrganizations(availableOrgs);
  }, [availableOrgs]);

  // Filter organizations based on search term
  useEffect(() => {
    if (!searchTerm) {
      setFilteredOrganizations(availableOrgs);
    } else {
      const filtered = availableOrgs.filter(
        (org) =>
          org.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          org.type?.toLowerCase().includes(searchTerm.toLowerCase()),
      );
      setFilteredOrganizations(filtered);
    }
    setHighlightedIndex(-1);
  }, [searchTerm, availableOrgs]);

  const handleOrganizationSelect = async (orgId: string) => {
    try {
      setIsDropdownOpen(false);
      await switchOrganization(orgId);
      const selectedOrg = availableOrgs.find(org => org.id === orgId);
      toast.success(`Switched to ${selectedOrg?.name}`);
      navigate("/dashboard", { replace: true });
    } catch (err) {
      console.error("Failed to switch organization:", err);
      toast.error("Failed to switch organization");
    }
  };

  // Handle edit organization click
  const handleEditOrganization = (e: React.MouseEvent, orgId: string) => {
    e.preventDefault();
    e.stopPropagation();
    navigate(`/organizations/${orgId}/settings`);
  };

  const canSwitchOrgs = isAdmin && (isSystemAdmin || hasMultipleOrgs);

  if (!currentOrg && availableOrgs.length === 0) {
    return (
      <Button
        variant="outline"
        className="h-9 px-4 py-2 flex items-center gap-2 w-[300px] border-input"
        disabled
      >
        <div className="flex items-center gap-2">
          <Building2 className="h-5 w-5 text-muted-foreground" />
          <span className="truncate font-medium">No Organization</span>
        </div>
      </Button>
    );
  }

  if (!canSwitchOrgs) {
    return (
      <Button
        variant="outline"
        className="h-9 px-4 py-2 flex items-center gap-2 w-[300px] border-input"
        disabled={isLoading}
      >
        <div className="flex items-center gap-2">
          <Building2 className="h-5 w-5 text-muted-foreground" />
          <span className="truncate font-medium">
            {currentOrg?.name || "Select Organization"}
          </span>
        </div>
      </Button>
    );
  }

  return (
    <DropdownMenu
      open={isDropdownOpen}
      onOpenChange={(open) => {
        setIsDropdownOpen(open);
        if (!open) {
          setTimeout(() => {
            setSearchTerm("");
            setHighlightedIndex(-1);
          }, 150);
        } else {
          setTimeout(() => {
            searchInputRef.current?.focus();
          }, 100);
        }
      }}
    >
      <DropdownMenuTrigger asChild disabled={isLoading}>
        <Button
          variant="outline"
          className="h-9 px-4 py-2 flex items-center justify-between gap-2 w-[300px] border-input hover:ring-1 hover:ring-ring/30 hover:ring-offset-0 hover:bg-accent/50 focus-visible:ring-1 focus-visible:ring-ring/30 focus-visible:ring-offset-0 data-[state=open]:ring-1 data-[state=open]:ring-ring/30 data-[state=open]:ring-offset-0 data-[state=open]:bg-accent/50"
        >
          <div className="flex items-center gap-2">
            <Building2 className="h-5 w-5 text-muted-foreground" />
            <span className="truncate font-medium">
              {currentOrg?.name || "Select Organization"}
            </span>
          </div>
          <ChevronDown className="h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent
        align="center"
        className="w-[380px] border-input shadow-sm"
      >
        <DropdownMenuLabel>Switch Organization</DropdownMenuLabel>
        <DropdownMenuSeparator className="opacity-50" />

        {/* Search Input - only show if there are multiple organizations */}
        {availableOrgs.length > 3 && (
          <>
            <div className="px-2 py-1">
              <div className="relative">
                <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground pointer-events-none" />
                <Input
                  ref={searchInputRef}
                  placeholder="Search organizations..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-8 h-8 bg-transparent border-input focus-visible:ring-1 focus-visible:ring-ring/30 focus-visible:ring-offset-0"
                  autoComplete="off"
                />
              </div>
            </div>
            <DropdownMenuSeparator className="opacity-50" />
          </>
        )}

        {/* Organizations List */}
        <div ref={dropdownRef} className="max-h-[300px] overflow-y-auto">
          {filteredOrganizations.map((org, index) => {
            const isCurrent = currentOrg && currentOrg.id === org.id;
            const Icon = getOrganizationIcon(org.type, org.hierarchy_level || 0);
            const indentLevel = (org.hierarchy_level || 0) * 16; // 16px per level

            return (
              <DropdownMenuItem
                key={org.id}
                disabled={isLoading || !!isCurrent}
                className={`flex items-center justify-between data-[highlighted]:bg-muted/50 hover:!bg-muted/50 ${
                  highlightedIndex === index ? "bg-muted/50" : ""
                }`}
                onSelect={(e) => {
                  e.preventDefault();
                  handleOrganizationSelect(org.id);
                }}
                onMouseEnter={() => setHighlightedIndex(index)}
                data-index={index}
              >
                <div className="flex items-center gap-2 flex-1" style={{ paddingLeft: `${indentLevel}px` }}>
                  <Icon className="h-4 w-4 text-muted-foreground flex-shrink-0" />
                  <div className="flex flex-col min-w-0 flex-1">
                    <span className={`truncate ${org.hierarchy_level === 0 ? 'font-semibold' : 'font-medium'}`}>
                      {org.name}
                    </span>
                    {org.type !== 'system' && (
                      <span className="text-xs text-muted-foreground capitalize">
                        {org.type.replace('_', ' ')}
                      </span>
                    )}
                  </div>
                  {isCurrent && (
                    <span className="ml-2 text-xs text-emerald-500/60 font-medium">
                      (Current)
                    </span>
                  )}
                </div>
                {isSystemAdmin && (
                  <Button
                    data-edit="true"
                    variant="ghost"
                    size="sm"
                    className="h-6 w-6 p-0 ml-2 opacity-60 hover:opacity-100 hover:!bg-muted/50"
                    onMouseDown={(e) => handleEditOrganization(e, org.id)}
                  >
                    <Edit className="h-3 w-3" />
                  </Button>
                )}
              </DropdownMenuItem>
            );
          })}

          {filteredOrganizations.length === 0 && searchTerm && (
            <div className="px-2 py-4 text-center text-sm text-muted-foreground">
              No organizations found matching "{searchTerm}"
            </div>
          )}
        </div>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
