import { useAuth } from "@/hooks/useAuth";
import { useLocations } from "@/hooks/useLocations";
import { globalRequestCache } from "@/lib/global-request-cache";
import { supabase } from "@/lib/supabase";
import { useOrganizationStore } from "@/stores/organization-store";
import { ALL_LOCATIONS_ID } from "@/types/location";
import { Database } from "@spritely/supabase-types";
import { useCallback, useEffect, useMemo, useState } from "react";
import {
  useOrganizationFilter
} from "./useOrganizationFilter";

// Define the appointment type from the appointments table
type AppointmentRecord = Database["public"]["Tables"]["appointments"]["Row"];

export interface Appointment extends AppointmentRecord {
  // Joined data
  patient?: {
    id: string;
    first_name: string;
    last_name: string;
    email: string | null;
    phone: string | null;
  };
  department?: {
    id: string;
    name: string;
    location_id: string | null;
  };
  provider?: {
    id: string;
    first_name: string;
    last_name: string;
    provider_type: "doctor" | "nurse" | "specialist" | "admin";
  };
  organization?: {
    id: string;
    name: string;
  };
  // Computed fields
  patient_name?: string;
  department_name?: string;
  provider_name?: string;
  organization_name?: string;
  status_color?: string;
  is_today?: boolean;
  is_upcoming?: boolean;
  is_overdue?: boolean;
}

interface UseAppointmentOptions {
  limit?: number;
  offset?: number;
  status?: "scheduled" | "checked_in" | "in_progress" | "completed" | "cancelled" | "no_show";
  dateRange?: [Date, Date];
  todayOnly?: boolean;
  upcomingOnly?: boolean;
}

// Memoized helper functions
const getStatusColor = (status: string): string => {
  const statusColors = {
    scheduled: "blue",
    checked_in: "green",
    in_progress: "yellow",
    completed: "emerald",
    cancelled: "red",
    no_show: "orange",
  };
  return statusColors[status as keyof typeof statusColors] || "gray";
};

const processAppointment = (appointment: any): Appointment => {
  if (!appointment) {
    throw new Error("Appointment data is null or undefined");
  }

  const appointmentDate = new Date(appointment.appointment_date);
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  const tomorrow = new Date(today);
  tomorrow.setDate(tomorrow.getDate() + 1);

  return {
    ...appointment,
    patient_name: appointment.patient?.first_name && appointment.patient?.last_name
      ? `${appointment.patient.first_name} ${appointment.patient.last_name}`
      : "Unknown Patient",
    department_name: appointment.department?.name || "Unknown Department",
    provider_name: appointment.provider?.first_name && appointment.provider?.last_name
      ? `${appointment.provider.first_name} ${appointment.provider.last_name}`
      : "Unassigned",
    organization_name: appointment.organization?.name || "Unknown Organization",
    status_color: getStatusColor(appointment.status),
    is_today: appointmentDate >= today && appointmentDate < tomorrow,
    is_upcoming: appointmentDate > new Date(),
    is_overdue: appointmentDate < new Date() && appointment.status === "scheduled",
  };
};

export function useAppointments(options: UseAppointmentOptions = {}) {
  const { organization } = useAuth();
  const { selectedLocation, shouldShowLocationSelector } = useLocations();
  const organizationFilter = useOrganizationFilter();
  const { isSystemAdmin } = useOrganizationStore();
  const [appointments, setAppointments] = useState<Appointment[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const [totalCount, setTotalCount] = useState(0);


  // Simplified memoization of key dependencies
  const isSystemAdminAllOrgs = isSystemAdmin && organization?.id === "system-admin-all-orgs";
  const shouldFilterByLocation = shouldShowLocationSelector && selectedLocation?.id !== ALL_LOCATIONS_ID;
  const locationId = shouldFilterByLocation ? selectedLocation?.id : null;

  // Simplified cache key generation
  const cacheKey = useMemo(() => {
    const filters = {
      org: isSystemAdminAllOrgs ? 'all' : organization?.id,
      loc: locationId || 'all',
      status: options.status || 'all',
      dateRange: options.dateRange ? options.dateRange.map(d => d.toISOString().split('T')[0]).join('-') : 'all',
      limit: options.limit || 'all',
      today: options.todayOnly || false,
      upcoming: options.upcomingOnly || false,
    };
    return `appointments:${JSON.stringify(filters)}`;
  }, [
    isSystemAdminAllOrgs,
    organization?.id,
    locationId,
    options.status,
    options.dateRange,
    options.limit,
    options.todayOnly,
    options.upcomingOnly,
  ]);

  const fetchAppointments = useCallback(async () => {
    if (!organization) {
      setIsLoading(false);
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const result = await globalRequestCache.getOrCreate(cacheKey, async () => {
        let query = supabase.from("appointments").select(
          `
          *,
          patient:patients(id, first_name, last_name, email, phone),
          department:departments(id, name, location_id),
          provider:healthcare_providers(id, first_name, last_name, provider_type),
          organization:organizations(id, name)
        `,
          { count: "exact" },
        );

        // Apply organization filter with hierarchical support
        if (!isSystemAdminAllOrgs) {
          // Check if this organization has children (is a parent organization)
          const { data: descendants } = await supabase
            .from("organizations")
            .select("id")
            .like("hierarchy_path", `${organization.hierarchy_path || organization.id}.%`);

          const descendantIds = descendants?.map(d => d.id) || [];
          const allOrgIds = [organization.id, ...descendantIds];

          // If there are descendants, use IN filter, otherwise use EQ filter
          if (descendantIds.length > 0) {
            query = query.in("organization_id", allOrgIds);
          } else {
            query = query.eq("organization_id", organization.id);
          }
        }

        if (locationId) {
          query = query.eq('departments.location_id', locationId);
        }

        if (options.status) {
          query = query.eq("status", options.status);
        }

        if (options.dateRange) {
          query = query
            .gte("appointment_date", options.dateRange[0].toISOString())
            .lte("appointment_date", options.dateRange[1].toISOString());
        }

        if (options.todayOnly) {
          const today = new Date();
          today.setHours(0, 0, 0, 0);
          const tomorrow = new Date(today);
          tomorrow.setDate(tomorrow.getDate() + 1);
          query = query
            .gte("appointment_date", today.toISOString())
            .lt("appointment_date", tomorrow.toISOString());
        }

        if (options.upcomingOnly) {
          query = query.gte("appointment_date", new Date().toISOString());
        }

        if (options.limit) {
          query = query.limit(options.limit);
        }

        if (options.offset) {
          query = query.range(options.offset, options.offset + (options.limit || 10) - 1);
        }

        query = query.order("appointment_date", { ascending: true });

        const { data, error: fetchError, count } = await query;
        if (fetchError) throw new Error(fetchError.message);
        return { data, count };
      });

      // Process appointments with memoized helper
      const processedAppointments = (result.data || []).map(processAppointment);
      setAppointments(processedAppointments);
      if (result.count !== null) setTotalCount(result.count);
    } catch (err) {
      console.error("Error fetching appointments:", err);
      setError(err instanceof Error ? err : new Error("Failed to fetch appointments"));
    } finally {
      setIsLoading(false);
    }
  }, [cacheKey, organization, isSystemAdminAllOrgs, locationId, organizationFilter, options]);

  useEffect(() => {
    fetchAppointments();
  }, [fetchAppointments]);

  return {
    appointments,
    isLoading,
    error,
    totalCount,
    isLocationFiltered: shouldFilterByLocation,
    currentLocation: selectedLocation,
  };
}
