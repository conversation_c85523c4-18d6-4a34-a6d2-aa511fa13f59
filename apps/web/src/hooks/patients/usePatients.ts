import { useAuth } from "@/hooks/useAuth";
import { useUserRoles } from "@/hooks/useUserRoles";
import { supabase } from "@/lib/supabase";
import {
    PatientFilters,
    PatientStats,
    PatientWithStats,
    UsePatientOptions,
} from "@/types/patient";
import { Database } from "@spritely/supabase-types";
import { useCallback, useEffect, useRef, useState } from "react";

type PatientRow = Database["public"]["Tables"]["patients"]["Row"];
type AppointmentRow = Database["public"]["Tables"]["appointments"]["Row"];

export function usePatients(options: UsePatientOptions = {}) {
  const { organization } = useAuth();
  const { isSystemAdmin, isLoading: rolesLoading, roles } = useUserRoles();
  const [patients, setPatients] = useState<PatientWithStats[]>([]);
  const [stats] = useState<PatientStats | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const [totalCount, setTotalCount] = useState(0);

  // Track the last processed values to prevent unnecessary fetches
  const lastProcessedRef = useRef<{
    organizationId?: string;
    limit?: number;
    offset?: number;
    filters?: PatientFilters;
  }>({});

  const {
    limit = 50,
    offset = 0,
    filters = {} as PatientFilters,
    includeAppointments = false,
    includeOrganization = false,
  } = options;

  const fetchPatients = useCallback(async () => {
    if (!organization) {
      setPatients([]);
      setIsLoading(false);
      return;
    }

    // Wait for user roles to load before proceeding with organization checks
    // Always wait if roles are still loading or if roles array is empty
    if (rolesLoading || roles.length === 0) {
      return;
    }

    // Check if we need to process (values have changed)
    const lastProcessed = lastProcessedRef.current;
    const hasChanged =
      lastProcessed.organizationId !== organization.id ||
      lastProcessed.limit !== limit ||
      lastProcessed.offset !== offset ||
      JSON.stringify(lastProcessed.filters) !== JSON.stringify(filters);

    // Update the ref immediately to prevent duplicate processing
    lastProcessedRef.current = {
      organizationId: organization.id,
      limit,
      offset,
      filters: { ...filters },
    };

    if (!hasChanged) {
      return; // No changes, skip processing
    }

    setIsLoading(true);
    setError(null);

    try {
      // Build the select query
      const selectParts = ["*"];

      if (includeAppointments) {
        selectParts.push(`
          appointments:appointments(
            id,
            appointment_date,
            status,
            reason
          )
        `);
      }

      if (includeOrganization) {
        selectParts.push(`
          organization:organizations(
            id,
            name
          )
        `);
      }

      const selectQuery = selectParts.join(",");

      // Start with the select query and count
      let query = supabase
        .from("patients")
        .select(selectQuery, { count: "exact" });

      // Apply organization filter with hierarchical support
      if (organization.id !== "system-admin-all-orgs") {
        // Check if this organization has children (is a parent organization)
        const { data: descendants } = await supabase
          .from("organizations")
          .select("id")
          .like("hierarchy_path", `${organization.hierarchy_path || organization.id}.%`);

        const descendantIds = descendants?.map(d => d.id) || [];
        const allOrgIds = [organization.id, ...descendantIds];

        // If there are descendants, use IN filter, otherwise use EQ filter
        if (descendantIds.length > 0) {
          query = query.in("organization_id", allOrgIds);
        } else {
          query = query.eq("organization_id", organization.id);
        }
      } else {
        // For "All Organizations" view, verify user is actually a system admin
        if (!isSystemAdmin) {
          // Non-system admins should never see all organizations
          throw new Error("Unauthorized access to all organizations");
        }

        // No organization filter needed for system admin viewing all organizations
      }

      // Apply filters
      if (filters.search) {
        query = query.or(
          `first_name.ilike.%${filters.search}%,last_name.ilike.%${filters.search}%,email.ilike.%${filters.search}%`,
        );
      }

      if (filters.gender && filters.gender !== "all") {
        query = query.eq("gender", filters.gender);
      }

      if (filters.organizationId && isSystemAdmin) {
        query = query.eq("organization_id", filters.organizationId);
      }

      // Apply pagination and ordering
      query = query
        .range(offset, offset + limit - 1)
        .order("created_at", { ascending: false });

      const { data, error: fetchError, count } = await query;

      if (fetchError) throw new Error(fetchError.message);

      // Process the data to add computed fields
      const processedPatients: PatientWithStats[] = (data || []).map(
        (patient) => {
          // Type assertion for the patient data from Supabase query
          const typedPatient = patient as unknown as PatientRow & {
            appointments?: AppointmentRow[];
          };
          // Calculate age from date of birth
          const dob = new Date(typedPatient.date_of_birth);
          const ageDifMs = Date.now() - dob.getTime();
          const ageDate = new Date(ageDifMs);
          const age = Math.abs(ageDate.getUTCFullYear() - 1970);

          // Get appointment info
          const appointments = typedPatient.appointments || [];
          const sortedAppointments = appointments.sort(
            (a: AppointmentRow, b: AppointmentRow) =>
              new Date(b.appointment_date).getTime() -
              new Date(a.appointment_date).getTime(),
          );

          const lastVisit = sortedAppointments.find(
            (apt: AppointmentRow) =>
              apt.status === "completed" &&
              new Date(apt.appointment_date) < new Date(),
          )?.appointment_date;

          const nextAppointment = appointments.find(
            (apt: AppointmentRow) =>
              apt.status === "scheduled" &&
              new Date(apt.appointment_date) > new Date(),
          )?.appointment_date;

          // Determine status
          let status: "new" | "active" | "inactive" | "archived" = "new";
          if (appointments.length > 0) {
            const hasRecentActivity = appointments.some(
              (apt: AppointmentRow) => {
                const aptDate = new Date(apt.appointment_date);
                const thirtyDaysAgo = new Date();
                thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
                return aptDate > thirtyDaysAgo;
              },
            );
            status = hasRecentActivity ? "active" : "inactive";
          }

          return {
            ...typedPatient,
            age,
            full_name: `${typedPatient.first_name} ${typedPatient.last_name}`,
            last_visit: lastVisit,
            next_appointment: nextAppointment,
            status,
            appointment_count: appointments.length,
            appointments: includeAppointments ? appointments : undefined,
          };
        },
      );

      setPatients(processedPatients);
      if (count !== null) setTotalCount(count);

      // TODO: Implement stats calculation from fetched patients
      // For now, stats are disabled to avoid multiple queries
    } catch (err) {
      console.error("Error fetching patients:", err);
      setError(
        err instanceof Error ? err : new Error("Failed to fetch patients"),
      );
    } finally {
      setIsLoading(false);
    }
  }, [organization?.id, isSystemAdmin, rolesLoading, roles.length, limit, offset, filters, includeAppointments, includeOrganization]);

  useEffect(() => {
    fetchPatients();
  }, [fetchPatients]);

  const refetch = useCallback(() => {
    // Force a refetch by updating the ref
    lastProcessedRef.current = {};
    fetchPatients();
  }, [fetchPatients]);

  return {
    patients,
    stats,
    isLoading,
    error,
    totalCount,
    refetch,
  };
}
